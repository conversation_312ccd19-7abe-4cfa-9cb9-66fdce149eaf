import { useState, useRef, useEffect } from "react";
import { useUser } from "@clerk/clerk-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Send, Smile, Users, Circle } from "lucide-react";
import { useRoomMessages } from "@/hooks/useRoomMessages";
import { RoomMessage } from "@/lib/supabase";

interface RoomChatProps {
  roomId: string;
}

const RoomChat = ({ roomId }: RoomChatProps) => {
  const { user } = useUser();
  const {
    messages,
    loading,
    sendMessage,
    typingUsers,
    onlineUsers,
    handleTyping,
    stopTyping
  } = useRoomMessages(roomId);
  const [newMessage, setNewMessage] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !user) return;

    await sendMessage(newMessage.trim());
    setNewMessage("");
    stopTyping();
    inputRef.current?.focus();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessage(e.target.value);
    handleTyping();
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).format(date);
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const isConsecutiveMessage = (currentMsg: RoomMessage, prevMsg: RoomMessage | undefined) => {
    if (!prevMsg) return false;
    const currentTime = new Date(currentMsg.created_at).getTime();
    const prevTime = new Date(prevMsg.created_at).getTime();
    return (
      currentMsg.user_id === prevMsg.user_id &&
      currentMsg.message_type === 'message' &&
      prevMsg.message_type === 'message' &&
      (currentTime - prevTime) < 60000 // Within 1 minute
    );
  };

  return (
    <div className="flex flex-col h-full">
      {/* Online Users Header */}
      <div className="border-b border-slate-700 p-3">
        <div className="flex items-center gap-2 text-sm text-slate-300">
          <Users className="w-4 h-4" />
          <span>Online ({onlineUsers.length})</span>
          <div className="flex gap-1 ml-2">
            {onlineUsers.slice(0, 5).map((onlineUser) => (
              <div key={onlineUser.id} className="relative">
                <Avatar className="h-6 w-6">
                  <AvatarImage src={onlineUser.image} />
                  <AvatarFallback className="bg-slate-600 text-slate-200 text-xs">
                    {getInitials(onlineUser.name)}
                  </AvatarFallback>
                </Avatar>
                <Circle className="absolute -bottom-0.5 -right-0.5 w-2 h-2 fill-green-500 text-green-500" />
              </div>
            ))}
            {onlineUsers.length > 5 && (
              <Badge variant="secondary" className="h-6 text-xs">
                +{onlineUsers.length - 5}
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Chat Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.map((message, index) => {
            const prevMessage = index > 0 ? messages[index - 1] : undefined;
            const isConsecutive = isConsecutiveMessage(message, prevMessage);
            const isOwnMessage = message.user_id === user?.id;

            if (message.message_type === 'system') {
              return (
                <div key={message.id} className="flex justify-center">
                  <div className="bg-slate-700/50 text-slate-300 text-xs px-3 py-1 rounded-full">
                    {message.content}
                  </div>
                </div>
              );
            }

            return (
              <div key={message.id} className={`flex gap-3 ${isConsecutive ? 'mt-1' : 'mt-4'}`}>
                {!isConsecutive && (
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarImage src={message.user_image} />
                    <AvatarFallback className="bg-slate-600 text-slate-200 text-xs">
                      {getInitials(message.user_name)}
                    </AvatarFallback>
                  </Avatar>
                )}

                <div className={`flex-1 ${isConsecutive ? 'ml-11' : ''}`}>
                  {!isConsecutive && (
                    <div className="flex items-center gap-2 mb-1">
                      <span className={`text-sm font-medium ${
                        isOwnMessage ? 'text-blue-400' : 'text-slate-300'
                      }`}>
                        {message.user_name}
                      </span>
                      <span className="text-xs text-slate-500">
                        {formatTime(message.created_at)}
                      </span>
                    </div>
                  )}
                  
                  <div className={`inline-block max-w-full break-words ${
                    isOwnMessage 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-slate-700 text-slate-100'
                  } px-3 py-2 rounded-lg text-sm`}>
                    {message.content}
                  </div>
                </div>
              </div>
            );
          })}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Typing Indicator */}
      {typingUsers.length > 0 && (
        <div className="px-4 py-2 text-xs text-slate-400">
          <div className="flex items-center gap-2">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <span>
              {typingUsers.length === 1
                ? `${typingUsers[0].name} is typing...`
                : typingUsers.length === 2
                ? `${typingUsers[0].name} and ${typingUsers[1].name} are typing...`
                : `${typingUsers[0].name} and ${typingUsers.length - 1} others are typing...`
              }
            </span>
          </div>
        </div>
      )}

      {/* Message Input */}
      <div className="border-t border-slate-700 p-4">
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              placeholder="Type a message..."
              value={newMessage}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              onBlur={stopTyping}
              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 pr-10"
              maxLength={500}
            />
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0 text-slate-400 hover:text-slate-300"
            >
              <Smile className="w-4 h-4" />
            </Button>
          </div>
          <Button
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 text-white px-3"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
        <div className="flex justify-between items-center mt-2 text-xs text-slate-500">
          <span>Press Enter to send, Shift+Enter for new line</span>
          <span>{newMessage.length}/500</span>
        </div>
      </div>
    </div>
  );
};

export default RoomChat;
