import { useState, useEffect } from 'react';
import { useAuth, useUser } from '@clerk/clerk-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { createAuthenticatedSupabaseClient, supabase } from '@/lib/supabase';
import { toast } from 'sonner';

export const RealtimeDebugger = ({ roomId }: { roomId: string }) => {
  const { getToken, isSignedIn } = useAuth();
  const { user } = useUser();
  const [messages, setMessages] = useState<any[]>([]);
  const [testMessage, setTestMessage] = useState('');
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!roomId || !user) return;

    let channel: any = null;

    const setupTest = async () => {
      try {
        console.log('Setting up realtime test for room:', roomId);
        
        // Get authenticated client
        const client = await getAuthenticatedClient();
        
        // Create channel
        channel = client
          .channel(`test-room:${roomId}`)
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'room_messages',
              filter: `room_id=eq.${roomId}`
            },
            (payload) => {
              console.log('Realtime test - message received:', payload);
              setMessages(prev => [...prev, {
                id: payload.new.id,
                content: payload.new.content,
                user_name: payload.new.user_name,
                created_at: payload.new.created_at,
                source: 'realtime'
              }]);
            }
          )
          .subscribe((status, err) => {
            console.log('Realtime test - subscription status:', status);
            setConnectionStatus(status);
            if (err) {
              console.error('Realtime test - subscription error:', err);
            }
          });

      } catch (error) {
        console.error('Realtime test setup error:', error);
        setConnectionStatus('error');
      }
    };

    setupTest();

    return () => {
      if (channel) {
        console.log('Cleaning up realtime test');
        supabase.removeChannel(channel);
      }
    };
  }, [roomId, user]);

  const getAuthenticatedClient = async () => {
    if (!isSignedIn) {
      return supabase;
    }

    try {
      const token = await getToken({ template: 'supabase' });
      if (token) {
        console.log('Got Clerk token for realtime test');
        return createAuthenticatedSupabaseClient(token);
      }
      return supabase;
    } catch (error) {
      console.error('Error getting authenticated client for test:', error);
      return supabase;
    }
  };

  const sendTestMessage = async () => {
    if (!testMessage.trim() || !user) return;

    setLoading(true);
    try {
      const client = await getAuthenticatedClient();
      
      const messageData = {
        room_id: roomId,
        user_id: user.id,
        user_name: user.fullName || user.firstName || 'Test User',
        user_image: user.imageUrl,
        content: `[TEST] ${testMessage.trim()}`,
        message_type: 'message'
      };

      console.log('Sending test message:', messageData);

      const { data, error } = await client
        .from('room_messages')
        .insert(messageData)
        .select()
        .single();

      if (error) {
        console.error('Test message insert error:', error);
        throw error;
      }

      console.log('Test message inserted successfully:', data);
      
      // Add to local messages for comparison
      setMessages(prev => [...prev, {
        id: data.id,
        content: data.content,
        user_name: data.user_name,
        created_at: data.created_at,
        source: 'direct'
      }]);

      setTestMessage('');
      toast.success('Test message sent');

    } catch (error: any) {
      console.error('Error sending test message:', error);
      toast.error(`Failed to send test message: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const clearMessages = () => {
    setMessages([]);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUBSCRIBED': return 'bg-green-500';
      case 'CHANNEL_ERROR': return 'bg-red-500';
      case 'TIMED_OUT': return 'bg-yellow-500';
      case 'CLOSED': return 'bg-gray-500';
      default: return 'bg-blue-500';
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Realtime Debugger</CardTitle>
        <CardDescription>
          Test realtime functionality for room: {roomId}
        </CardDescription>
        <div className="flex items-center gap-2">
          <span>Connection Status:</span>
          <Badge className={getStatusColor(connectionStatus)}>
            {connectionStatus}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Input
            placeholder="Enter test message..."
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && sendTestMessage()}
          />
          <Button onClick={sendTestMessage} disabled={loading || !testMessage.trim()}>
            Send Test
          </Button>
          <Button variant="outline" onClick={clearMessages}>
            Clear
          </Button>
        </div>

        <div className="space-y-2 max-h-60 overflow-y-auto">
          <h4 className="font-medium">Messages ({messages.length}):</h4>
          {messages.length === 0 ? (
            <p className="text-muted-foreground text-sm">No messages yet</p>
          ) : (
            messages.map((msg, index) => (
              <div key={index} className="p-2 border rounded text-sm">
                <div className="flex justify-between items-start">
                  <div>
                    <strong>{msg.user_name}:</strong> {msg.content}
                  </div>
                  <Badge variant={msg.source === 'realtime' ? 'default' : 'secondary'}>
                    {msg.source}
                  </Badge>
                </div>
                <div className="text-xs text-muted-foreground">
                  {new Date(msg.created_at).toLocaleTimeString()}
                </div>
              </div>
            ))
          )}
        </div>

        <div className="text-xs text-muted-foreground">
          <p>• "realtime" messages come through the subscription</p>
          <p>• "direct" messages are from the insert response</p>
          <p>• Both should appear for working realtime</p>
        </div>
      </CardContent>
    </Card>
  );
};
