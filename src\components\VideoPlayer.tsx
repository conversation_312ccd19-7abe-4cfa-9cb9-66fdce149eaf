
import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Upload,
  Link,
  Maximize,
  SkipBack,
  SkipForward
} from "lucide-react";
import { extractYouTubeVideoId, isYouTubeUrl, getYouTubeEmbedUrl } from "@/utils/youtube";
import { useRoomVideoState } from "@/hooks/useRoomVideoState";

interface VideoPlayerProps {
  roomId: string;
  isMuted: boolean;
  onMuteToggle: () => void;
  isOwner: boolean;
  onPlayingStateChange?: (isPlaying: boolean) => void;
}

const VideoPlayer = ({ roomId, isMuted, onMuteToggle, isOwner, onPlayingStateChange }: VideoPlayerProps) => {
  const { videoState, loading, setVideoUrl, setPlaying, setCurrentTime, setDuration } = useRoomVideoState(roomId, isOwner);
  const [inputUrl, setInputUrl] = useState("");
  const [showUrlInput, setShowUrlInput] = useState(false);
  const [volume, setVolume] = useState(1);
  const [youTubeApiStatus, setYouTubeApiStatus] = useState<'loading' | 'loaded' | 'failed' | 'idle'>('idle');
  const [useFallbackEmbed, setUseFallbackEmbed] = useState(false);
  const [corsError, setCorsError] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const youTubePlayerRef = useRef<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Derived state from videoState
  const hasVideo = !!videoState?.video_url;
  const isPlaying = videoState?.is_playing || false;
  const currentTime = videoState?.video_current_time || 0;
  const duration = videoState?.video_duration || 0;
  const isYouTubeVideo = videoState?.video_type === 'youtube';
  const youTubeVideoId = videoState?.youtube_video_id;

  // Notify parent component of playing state changes
  useEffect(() => {
    onPlayingStateChange?.(isPlaying);
  }, [isPlaying, onPlayingStateChange]);

  // For YouTube videos, use fallback embed due to CORS issues
  useEffect(() => {
    if (isYouTubeVideo && youTubeVideoId) {
      // Immediately use fallback embed for YouTube videos to avoid CORS issues
      setUseFallbackEmbed(true);
      setCorsError(false);
    }
  }, [isYouTubeVideo, youTubeVideoId]);

  // Sync video playback with room state (for non-YouTube videos)
  useEffect(() => {
    if (!isYouTubeVideo && videoRef.current) {
      if (isPlaying) {
        videoRef.current.play().catch(error => {
          console.error('Error playing video:', error);
        });
      } else {
        videoRef.current.pause();
      }
    }
  }, [isPlaying, isYouTubeVideo]);

  // Sync video time with room state (for non-YouTube videos)
  useEffect(() => {
    if (!isYouTubeVideo && videoRef.current) {
      try {
        const playerTime = videoRef.current.currentTime;
        if (Math.abs(playerTime - currentTime) > 2) { // Only sync if difference > 2 seconds
          videoRef.current.currentTime = currentTime;
        }
      } catch (error) {
        console.error('Error syncing video time:', error);
      }
    }
  }, [currentTime, isYouTubeVideo]);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.muted = isMuted;
    }
  }, [isMuted]);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('video/') && isOwner) {
      const url = URL.createObjectURL(file);
      setVideoUrl(url, 'file');
    }
  };

  const handleUrlSubmit = () => {
    if (inputUrl.trim() && isOwner) {
      const trimmedUrl = inputUrl.trim();

      // Check if it's a YouTube URL
      if (isYouTubeUrl(trimmedUrl)) {
        const videoId = extractYouTubeVideoId(trimmedUrl);
        if (videoId) {
          setVideoUrl(trimmedUrl, 'youtube', videoId);
          setShowUrlInput(false);
          setInputUrl("");
          return;
        }
      }

      // Handle regular video URLs
      setVideoUrl(trimmedUrl, 'url');
      setShowUrlInput(false);
      setInputUrl("");
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current && isOwner) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current && isOwner) {
      setDuration(videoRef.current.duration);
    }
  };

  const handleSeek = (time: number) => {
    if (isOwner && !isYouTubeVideo) {
      try {
        if (videoRef.current) {
          videoRef.current.currentTime = time;
        }
        setCurrentTime(time);
      } catch (error) {
        console.error('Error seeking video:', error);
      }
    }
  };

  const handlePlayPause = () => {
    if (isOwner && !isYouTubeVideo) {
      setPlaying(!isPlaying);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume);
    try {
      if (videoRef.current) {
        videoRef.current.volume = newVolume;
      }
    } catch (error) {
      console.error('Error changing volume:', error);
    }
  };

  if (!hasVideo) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-slate-900">
        <Card className="w-full max-w-md mx-4 bg-slate-800 border-slate-700">
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-6">
              <Play className="w-8 h-8 text-slate-300" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">No Video Loaded</h3>
            <p className="text-slate-400 mb-6">Upload a video file or add a YouTube URL to start watching together.</p>
            
            {isOwner ? (
              <div className="space-y-4">
                <Button 
                  onClick={() => fileInputRef.current?.click()}
                  className="w-full bg-slate-700 hover:bg-slate-600 text-white"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Video File
                </Button>
                
                <Button 
                  onClick={() => setShowUrlInput(!showUrlInput)}
                  variant="outline"
                  className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
                >
                  <Link className="w-4 h-4 mr-2" />
                  Add Video URL
                </Button>

                {showUrlInput && (
                  <div className="flex gap-2 mt-4">
                    <Input
                      placeholder="Enter YouTube URL or video URL..."
                      value={inputUrl}
                      onChange={(e) => setInputUrl(e.target.value)}
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                    />
                    <Button onClick={handleUrlSubmit} size="sm">
                      Add
                    </Button>
                  </div>
                )}

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="video/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>
            ) : (
              <p className="text-slate-500 text-sm">Only the room owner can add videos.</p>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  // YouTube Embed Component (fallback for CORS issues)
  const YouTubeEmbedPlayer = () => {
    if (!youTubeVideoId) return null;

    const embedUrl = getYouTubeEmbedUrl(youTubeVideoId, {
      autoplay: false,
      mute: isMuted,
      controls: true
    });

    return (
      <div className="relative w-full h-full">
        <iframe
          src={embedUrl}
          className="w-full h-full border-0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          title="YouTube video player"
        />
        <div className="absolute top-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
          <span>YouTube Player</span>
        </div>
        {corsError && (
          <div className="absolute bottom-2 left-2 bg-amber-600 bg-opacity-90 text-white text-xs px-2 py-1 rounded">
            <span>Limited controls due to CORS restrictions</span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="relative w-full h-full bg-black group">
      {isYouTubeVideo ? (
        <YouTubeEmbedPlayer />
      ) : (
        <>
          <video
            ref={videoRef}
            src={videoState?.video_url}
            className="w-full h-full object-contain"
            onTimeUpdate={handleTimeUpdate}
            onLoadedMetadata={handleLoadedMetadata}
            controls={false}
          />
          
          {/* Video Controls Overlay - Only for non-YouTube videos */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className="absolute bottom-0 left-0 right-0 p-6">
              {/* Progress Bar */}
              <div className="mb-4">
                <div className="flex items-center gap-2 text-white text-sm mb-2">
                  <span>{formatTime(currentTime)}</span>
                  <span>/</span>
                  <span>{formatTime(duration)}</span>
                </div>
                <div 
                  className="w-full h-2 bg-slate-700 rounded-full cursor-pointer"
                  onClick={(e) => {
                    if (isOwner && duration > 0) {
                      const rect = e.currentTarget.getBoundingClientRect();
                      const percent = (e.clientX - rect.left) / rect.width;
                      handleSeek(percent * duration);
                    }
                  }}
                >
                  <div 
                    className="h-full bg-white rounded-full transition-all duration-150"
                    style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                  />
                </div>
              </div>

              {/* Control Buttons */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {isOwner && (
                    <>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSeek(Math.max(0, currentTime - 10))}
                        className="text-white hover:bg-white/20"
                      >
                        <SkipBack className="w-5 h-5" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handlePlayPause}
                        className="text-white hover:bg-white/20"
                      >
                        {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSeek(Math.min(duration, currentTime + 10))}
                        className="text-white hover:bg-white/20"
                      >
                        <SkipForward className="w-5 h-5" />
                      </Button>
                    </>
                  )}
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onMuteToggle}
                      className="text-white hover:bg-white/20"
                    >
                      {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                    </Button>
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={isMuted ? 0 : volume}
                      onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                      className="w-20 h-1 bg-slate-600 rounded-lg appearance-none cursor-pointer"
                    />
                  </div>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    videoRef.current?.requestFullscreen();
                  }}
                  className="text-white hover:bg-white/20"
                >
                  <Maximize className="w-5 h-5" />
                </Button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default VideoPlayer;
